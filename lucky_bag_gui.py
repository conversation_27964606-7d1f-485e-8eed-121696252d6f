#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抢福袋自动化脚本 - GUI版本
Lucky Bag Auto Grabber - GUI Version
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import time
from lucky_bag_grabber import LuckyBagGrabber
import logging

class LuckyBagGUI:
    """抢福袋GUI界面"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("抢福袋自动化脚本 v1.0")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 初始化抢夺器
        self.grabber = LuckyBagGrabber()
        
        # 创建界面
        self.create_widgets()
        
        # 设置日志处理器
        self.setup_logging()
        
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 房间设置区域
        room_frame = ttk.LabelFrame(main_frame, text="房间设置", padding="5")
        room_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        room_frame.columnconfigure(1, weight=1)
        
        ttk.Label(room_frame, text="房间ID:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.room_id_var = tk.StringVar()
        self.room_id_entry = ttk.Entry(room_frame, textvariable=self.room_id_var, width=20)
        self.room_id_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 5))
        
        self.set_room_btn = ttk.Button(room_frame, text="设置房间", command=self.set_room)
        self.set_room_btn.grid(row=0, column=2, padx=(5, 0))
        
        # Cookie设置区域
        cookie_frame = ttk.LabelFrame(main_frame, text="Cookie设置 (可选)", padding="5")
        cookie_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        cookie_frame.columnconfigure(0, weight=1)
        
        self.cookie_text = scrolledtext.ScrolledText(cookie_frame, height=3, wrap=tk.WORD)
        self.cookie_text.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        
        cookie_btn_frame = ttk.Frame(cookie_frame)
        cookie_btn_frame.grid(row=1, column=0, sticky=tk.W)
        
        self.set_cookie_btn = ttk.Button(cookie_btn_frame, text="设置Cookie", command=self.set_cookie)
        self.set_cookie_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.clear_cookie_btn = ttk.Button(cookie_btn_frame, text="清除Cookie", command=self.clear_cookie)
        self.clear_cookie_btn.pack(side=tk.LEFT)
        
        # 抢夺设置区域
        settings_frame = ttk.LabelFrame(main_frame, text="抢夺设置", padding="5")
        settings_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(settings_frame, text="抢夺间隔(秒):").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.interval_var = tk.StringVar(value="0.1")
        self.interval_entry = ttk.Entry(settings_frame, textvariable=self.interval_var, width=10)
        self.interval_entry.grid(row=0, column=1, sticky=tk.W, padx=(0, 10))
        
        ttk.Label(settings_frame, text="最大尝试次数:").grid(row=0, column=2, sticky=tk.W, padx=(0, 5))
        self.max_attempts_var = tk.StringVar(value="1000")
        self.max_attempts_entry = ttk.Entry(settings_frame, textvariable=self.max_attempts_var, width=10)
        self.max_attempts_entry.grid(row=0, column=3, sticky=tk.W)
        
        # 控制按钮区域
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=3, column=0, columnspan=2, pady=(0, 10))
        
        self.start_btn = ttk.Button(control_frame, text="开始抢福袋", command=self.start_grab)
        self.start_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.stop_btn = ttk.Button(control_frame, text="停止抢福袋", command=self.stop_grab, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.clear_log_btn = ttk.Button(control_frame, text="清除日志", command=self.clear_log)
        self.clear_log_btn.pack(side=tk.LEFT)
        
        # 状态显示区域
        status_frame = ttk.LabelFrame(main_frame, text="运行状态", padding="5")
        status_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        status_frame.columnconfigure(1, weight=1)
        
        ttk.Label(status_frame, text="状态:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.status_var = tk.StringVar(value="未开始")
        self.status_label = ttk.Label(status_frame, textvariable=self.status_var, foreground="blue")
        self.status_label.grid(row=0, column=1, sticky=tk.W)
        
        # 日志显示区域
        log_frame = ttk.LabelFrame(main_frame, text="运行日志", padding="5")
        log_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(5, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, wrap=tk.WORD)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
    def setup_logging(self):
        """设置日志处理器"""
        class GUILogHandler(logging.Handler):
            def __init__(self, text_widget):
                super().__init__()
                self.text_widget = text_widget
                
            def emit(self, record):
                msg = self.format(record)
                def append():
                    self.text_widget.insert(tk.END, msg + '\n')
                    self.text_widget.see(tk.END)
                self.text_widget.after(0, append)
        
        # 添加GUI日志处理器
        gui_handler = GUILogHandler(self.log_text)
        gui_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        
        # 获取logger并添加处理器
        logger = logging.getLogger('lucky_bag_grabber')
        logger.addHandler(gui_handler)
        logger.setLevel(logging.INFO)
        
    def set_room(self):
        """设置房间ID"""
        room_id = self.room_id_var.get().strip()
        if not room_id:
            messagebox.showerror("错误", "请输入房间ID")
            return
        
        self.grabber.set_room_id(room_id)
        self.log_message(f"房间ID设置为: {room_id}")
        
    def set_cookie(self):
        """设置Cookie"""
        cookie_string = self.cookie_text.get("1.0", tk.END).strip()
        if not cookie_string:
            messagebox.showwarning("警告", "Cookie为空")
            return
        
        if self.grabber.set_cookies(cookie_string):
            self.log_message("Cookie设置成功")
            messagebox.showinfo("成功", "Cookie设置成功")
        else:
            messagebox.showerror("错误", "Cookie设置失败")
    
    def clear_cookie(self):
        """清除Cookie"""
        self.cookie_text.delete("1.0", tk.END)
        self.grabber.cookies = {}
        self.grabber.session.cookies.clear()
        self.log_message("Cookie已清除")
        
    def start_grab(self):
        """开始抢夺"""
        if not self.grabber.room_id:
            messagebox.showerror("错误", "请先设置房间ID")
            return
        
        try:
            # 设置参数
            interval = float(self.interval_var.get())
            max_attempts = int(self.max_attempts_var.get())
            
            self.grabber.set_grab_interval(interval)
            self.grabber.max_attempts = max_attempts
            
            # 开始抢夺
            if self.grabber.start_auto_grab():
                self.status_var.set("运行中")
                self.status_label.config(foreground="green")
                self.start_btn.config(state=tk.DISABLED)
                self.stop_btn.config(state=tk.NORMAL)
                self.log_message("开始自动抢福袋")
            else:
                messagebox.showerror("错误", "启动失败")
                
        except ValueError:
            messagebox.showerror("错误", "请输入有效的数字")
    
    def stop_grab(self):
        """停止抢夺"""
        self.grabber.stop_auto_grab()
        self.status_var.set("已停止")
        self.status_label.config(foreground="red")
        self.start_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)
        self.log_message("已停止自动抢福袋")
        
    def clear_log(self):
        """清除日志"""
        self.log_text.delete("1.0", tk.END)
        
    def log_message(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)

def main():
    """主函数"""
    root = tk.Tk()
    app = LuckyBagGUI(root)
    
    # 设置窗口关闭事件
    def on_closing():
        if app.grabber.is_running:
            app.stop_grab()
            time.sleep(0.5)
        root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()

if __name__ == "__main__":
    main()
