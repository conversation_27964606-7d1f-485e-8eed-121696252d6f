@echo off
chcp 65001 >nul
title 抢福袋自动化脚本

echo ========================================
echo           抢福袋自动化脚本 v1.0
echo ========================================
echo.

echo 请选择运行模式:
echo 1. 图形界面版本 (推荐)
echo 2. 命令行版本
echo 3. 退出
echo.

set /p choice=请输入选择 (1-3): 

if "%choice%"=="1" (
    echo 启动图形界面版本...
    python lucky_bag_gui.py
) else if "%choice%"=="2" (
    echo 启动命令行版本...
    python lucky_bag_grabber.py
) else if "%choice%"=="3" (
    echo 退出程序
    exit
) else (
    echo 无效选择，请重新运行
    pause
    exit
)

pause
