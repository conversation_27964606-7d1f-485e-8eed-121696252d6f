#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抢福袋自动化脚本 - GUI版本
Lucky Bag Auto Grabber - GUI Version
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import time
import webbrowser
import re
from urllib.parse import urlparse
from lucky_bag_grabber import LuckyBagGrabber
import logging

class LuckyBagGUI:
    """抢福袋GUI界面"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("抢福袋自动化脚本 v1.0")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 初始化抢夺器
        self.grabber = LuckyBagGrabber()
        
        # 创建界面
        self.create_widgets()
        
        # 设置日志处理器
        self.setup_logging()
        
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)

        # 网址登录区域
        login_frame = ttk.LabelFrame(main_frame, text="网址登录", padding="5")
        login_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        login_frame.columnconfigure(1, weight=1)

        ttk.Label(login_frame, text="直播间网址:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.url_var = tk.StringVar()
        self.url_entry = ttk.Entry(login_frame, textvariable=self.url_var, width=50)
        self.url_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 5))

        self.open_browser_btn = ttk.Button(login_frame, text="打开浏览器登录", command=self.open_browser_login)
        self.open_browser_btn.grid(row=0, column=2, padx=(5, 0))

        self.parse_url_btn = ttk.Button(login_frame, text="解析房间", command=self.parse_room_url)
        self.parse_url_btn.grid(row=0, column=3, padx=(5, 0))

        # 房间信息显示区域
        room_frame = ttk.LabelFrame(main_frame, text="房间信息", padding="5")
        room_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        room_frame.columnconfigure(1, weight=1)

        ttk.Label(room_frame, text="房间ID:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.room_id_var = tk.StringVar()
        self.room_id_label = ttk.Label(room_frame, textvariable=self.room_id_var, foreground="blue")
        self.room_id_label.grid(row=0, column=1, sticky=tk.W, padx=(0, 10))

        ttk.Label(room_frame, text="房间名称:").grid(row=0, column=2, sticky=tk.W, padx=(0, 5))
        self.room_name_var = tk.StringVar()
        self.room_name_label = ttk.Label(room_frame, textvariable=self.room_name_var, foreground="green")
        self.room_name_label.grid(row=0, column=3, sticky=tk.W)
        
        # Cookie设置区域
        cookie_frame = ttk.LabelFrame(main_frame, text="Cookie设置 (登录后自动获取)", padding="5")
        cookie_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        cookie_frame.columnconfigure(0, weight=1)

        self.cookie_text = scrolledtext.ScrolledText(cookie_frame, height=3, wrap=tk.WORD)
        self.cookie_text.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))

        cookie_btn_frame = ttk.Frame(cookie_frame)
        cookie_btn_frame.grid(row=1, column=0, sticky=tk.W)

        self.get_cookie_btn = ttk.Button(cookie_btn_frame, text="从浏览器获取Cookie", command=self.get_browser_cookie)
        self.get_cookie_btn.pack(side=tk.LEFT, padx=(0, 5))

        self.set_cookie_btn = ttk.Button(cookie_btn_frame, text="手动设置Cookie", command=self.set_cookie)
        self.set_cookie_btn.pack(side=tk.LEFT, padx=(0, 5))

        self.clear_cookie_btn = ttk.Button(cookie_btn_frame, text="清除Cookie", command=self.clear_cookie)
        self.clear_cookie_btn.pack(side=tk.LEFT)
        
        # 抢夺设置区域
        settings_frame = ttk.LabelFrame(main_frame, text="抢夺设置", padding="5")
        settings_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(settings_frame, text="抢夺间隔(秒):").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.interval_var = tk.StringVar(value="0.1")
        self.interval_entry = ttk.Entry(settings_frame, textvariable=self.interval_var, width=10)
        self.interval_entry.grid(row=0, column=1, sticky=tk.W, padx=(0, 10))
        
        ttk.Label(settings_frame, text="最大尝试次数:").grid(row=0, column=2, sticky=tk.W, padx=(0, 5))
        self.max_attempts_var = tk.StringVar(value="1000")
        self.max_attempts_entry = ttk.Entry(settings_frame, textvariable=self.max_attempts_var, width=10)
        self.max_attempts_entry.grid(row=0, column=3, sticky=tk.W)
        
        # 控制按钮区域
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=4, column=0, columnspan=2, pady=(0, 10))
        
        self.start_btn = ttk.Button(control_frame, text="开始抢福袋", command=self.start_grab)
        self.start_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.stop_btn = ttk.Button(control_frame, text="停止抢福袋", command=self.stop_grab, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.clear_log_btn = ttk.Button(control_frame, text="清除日志", command=self.clear_log)
        self.clear_log_btn.pack(side=tk.LEFT)
        
        # 状态显示区域
        status_frame = ttk.LabelFrame(main_frame, text="运行状态", padding="5")
        status_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        status_frame.columnconfigure(1, weight=1)

        ttk.Label(status_frame, text="状态:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.status_var = tk.StringVar(value="未开始")
        self.status_label = ttk.Label(status_frame, textvariable=self.status_var, foreground="blue")
        self.status_label.grid(row=0, column=1, sticky=tk.W, padx=(0, 20))

        ttk.Label(status_frame, text="福袋监控:").grid(row=0, column=2, sticky=tk.W, padx=(0, 5))
        self.bag_count_var = tk.StringVar(value="0个")
        self.bag_count_label = ttk.Label(status_frame, textvariable=self.bag_count_var, foreground="orange")
        self.bag_count_label.grid(row=0, column=3, sticky=tk.W)

        # 日志显示区域
        log_frame = ttk.LabelFrame(main_frame, text="运行日志", padding="5")
        log_frame.grid(row=6, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(6, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, wrap=tk.WORD)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
    def setup_logging(self):
        """设置日志处理器"""
        class GUILogHandler(logging.Handler):
            def __init__(self, text_widget):
                super().__init__()
                self.text_widget = text_widget
                
            def emit(self, record):
                msg = self.format(record)
                def append():
                    self.text_widget.insert(tk.END, msg + '\n')
                    self.text_widget.see(tk.END)
                self.text_widget.after(0, append)
        
        # 添加GUI日志处理器
        gui_handler = GUILogHandler(self.log_text)
        gui_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        
        # 获取logger并添加处理器
        logger = logging.getLogger('lucky_bag_grabber')
        logger.addHandler(gui_handler)
        logger.setLevel(logging.INFO)

    def open_browser_login(self):
        """打开浏览器进行登录"""
        url = self.url_var.get().strip()
        if not url:
            messagebox.showerror("错误", "请输入直播间网址")
            return

        # 确保URL格式正确
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url

        try:
            webbrowser.open(url)
            self.log_message(f"已打开浏览器: {url}")
            messagebox.showinfo("提示", "请在浏览器中登录，然后点击'从浏览器获取Cookie'按钮")
        except Exception as e:
            messagebox.showerror("错误", f"打开浏览器失败: {e}")

    def parse_room_url(self):
        """解析房间URL获取房间ID"""
        url = self.url_var.get().strip()
        if not url:
            messagebox.showerror("错误", "请输入直播间网址")
            return

        try:
            # 解析不同平台的房间ID
            room_id = None
            room_name = "未知房间"

            # 斗鱼直播间URL解析
            if 'douyu.com' in url:
                # 匹配 https://www.douyu.com/123456 格式
                match = re.search(r'douyu\.com/(\d+)', url)
                if match:
                    room_id = match.group(1)
                    room_name = f"斗鱼房间 {room_id}"

            # 虎牙直播间URL解析
            elif 'huya.com' in url:
                match = re.search(r'huya\.com/(\w+)', url)
                if match:
                    room_id = match.group(1)
                    room_name = f"虎牙房间 {room_id}"

            # Bilibili直播间URL解析
            elif 'live.bilibili.com' in url:
                match = re.search(r'live\.bilibili\.com/(\d+)', url)
                if match:
                    room_id = match.group(1)
                    room_name = f"B站房间 {room_id}"

            if room_id:
                self.room_id_var.set(room_id)
                self.room_name_var.set(room_name)
                self.grabber.set_room_id(room_id)
                self.log_message(f"解析成功 - 房间ID: {room_id}")

                # 尝试获取房间详细信息
                self.get_room_info()
            else:
                messagebox.showerror("错误", "无法解析房间ID，请检查URL格式")

        except Exception as e:
            messagebox.showerror("错误", f"解析URL失败: {e}")

    def get_room_info(self):
        """获取房间详细信息"""
        def fetch_info():
            try:
                room_info = self.grabber.get_room_info()
                if room_info:
                    room_name = room_info.get('room', {}).get('room_name', '未知房间')
                    self.room_name_var.set(room_name)
                    self.log_message(f"房间信息: {room_name}")
            except Exception as e:
                self.log_message(f"获取房间信息失败: {e}")

        # 在后台线程中获取房间信息
        threading.Thread(target=fetch_info, daemon=True).start()

    def get_browser_cookie(self):
        """从浏览器获取Cookie的说明"""
        help_text = """从浏览器获取Cookie的步骤：

1. 确保已在浏览器中登录目标网站
2. 按F12打开开发者工具
3. 切换到 Network(网络) 标签
4. 刷新页面或进行任意操作
5. 找到任意请求，点击查看详情
6. 在 Request Headers 中找到 Cookie
7. 复制完整的Cookie值到下方文本框
8. 点击"手动设置Cookie"按钮

提示：Cookie通常很长，请确保完整复制"""

        messagebox.showinfo("获取Cookie帮助", help_text)
        self.log_message("请按照提示从浏览器获取Cookie")
        
    def set_cookie(self):
        """设置Cookie"""
        cookie_string = self.cookie_text.get("1.0", tk.END).strip()
        if not cookie_string:
            messagebox.showwarning("警告", "Cookie为空")
            return
        
        if self.grabber.set_cookies(cookie_string):
            self.log_message("Cookie设置成功")
            messagebox.showinfo("成功", "Cookie设置成功")
        else:
            messagebox.showerror("错误", "Cookie设置失败")
    
    def clear_cookie(self):
        """清除Cookie"""
        self.cookie_text.delete("1.0", tk.END)
        self.grabber.cookies = {}
        self.grabber.session.cookies.clear()
        self.log_message("Cookie已清除")
        
    def start_grab(self):
        """开始抢夺"""
        if not self.grabber.room_id:
            messagebox.showerror("错误", "请先解析房间URL或设置房间ID")
            return

        if not self.grabber.cookies:
            result = messagebox.askyesno("警告", "未设置Cookie，可能无法正常抢夺福袋。\n\n是否继续？")
            if not result:
                return

        try:
            # 设置参数
            interval = float(self.interval_var.get())
            max_attempts = int(self.max_attempts_var.get())

            self.grabber.set_grab_interval(interval)
            self.grabber.max_attempts = max_attempts

            # 开始抢夺
            if self.grabber.start_auto_grab():
                self.status_var.set("扫描中...")
                self.status_label.config(foreground="green")
                self.start_btn.config(state=tk.DISABLED)
                self.stop_btn.config(state=tk.NORMAL)
                self.log_message("开始自动扫描福袋并抢夺")

                # 启动福袋计数更新线程
                self.start_bag_counter()
            else:
                messagebox.showerror("错误", "启动失败")

        except ValueError:
            messagebox.showerror("错误", "请输入有效的数字")

    def start_bag_counter(self):
        """启动福袋计数器"""
        def update_counter():
            while self.grabber.is_running:
                try:
                    bags = self.grabber.check_lucky_bag()
                    bag_count = len(bags)
                    self.bag_count_var.set(f"{bag_count}个")

                    if bag_count > 0:
                        self.status_var.set("发现福袋!")
                        self.status_label.config(foreground="red")
                    else:
                        self.status_var.set("扫描中...")
                        self.status_label.config(foreground="green")

                    time.sleep(1)  # 每秒更新一次计数
                except Exception as e:
                    self.log_message(f"更新福袋计数失败: {e}")
                    time.sleep(2)

        threading.Thread(target=update_counter, daemon=True).start()
    
    def stop_grab(self):
        """停止抢夺"""
        self.grabber.stop_auto_grab()
        self.status_var.set("已停止")
        self.status_label.config(foreground="red")
        self.bag_count_var.set("0个")
        self.start_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)
        self.log_message("已停止自动抢福袋")
        
    def clear_log(self):
        """清除日志"""
        self.log_text.delete("1.0", tk.END)
        
    def log_message(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)

def main():
    """主函数"""
    root = tk.Tk()
    app = LuckyBagGUI(root)
    
    # 设置窗口关闭事件
    def on_closing():
        if app.grabber.is_running:
            app.stop_grab()
            time.sleep(0.5)
        root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()

if __name__ == "__main__":
    main()
