@echo off
chcp 65001 >nul
title 抢福袋自动化脚本

echo ========================================
echo           抢福袋自动化脚本 v2.0
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python，请先安装Python 3.6+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit
)

echo ✅ Python环境检查通过

echo.
echo 正在检查依赖包...
python -c "import requests" >nul 2>&1
if errorlevel 1 (
    echo ❌ 缺少requests包，正在安装...
    pip install requests
    if errorlevel 1 (
        echo ❌ 安装失败，请手动执行: pip install requests
        pause
        exit
    )
)

python -c "import tkinter" >nul 2>&1
if errorlevel 1 (
    echo ❌ 缺少tkinter包，请安装Python完整版本
    pause
    exit
)

echo ✅ 依赖包检查通过

echo.
echo 请选择运行模式:
echo 1. 图形界面版本 (推荐) - 支持网址登录和自动扫描
echo 2. 命令行版本 - 简单快速
echo 3. 安装/更新依赖包
echo 4. 退出
echo.

set /p choice=请输入选择 (1-4):

if "%choice%"=="1" (
    echo.
    echo 🚀 启动图形界面版本...
    echo 💡 使用提示:
    echo    1. 输入直播间网址
    echo    2. 点击"打开浏览器登录"
    echo    3. 在浏览器中登录账号
    echo    4. 返回软件点击"从浏览器获取Cookie"
    echo    5. 点击"开始抢福袋"
    echo.
    python lucky_bag_gui.py
) else if "%choice%"=="2" (
    echo.
    echo 🚀 启动命令行版本...
    python lucky_bag_grabber.py
) else if "%choice%"=="3" (
    echo.
    echo 📦 安装/更新依赖包...
    pip install -r requirements.txt
    echo 安装完成!
    pause
    goto :start
) else if "%choice%"=="4" (
    echo 👋 退出程序
    exit
) else (
    echo ❌ 无效选择，请重新运行
    pause
    exit
)

:start
pause
