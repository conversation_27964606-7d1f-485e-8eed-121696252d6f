# 抢福袋自动化脚本 v2.0

一个用于自动抢夺直播间福袋的Python脚本，支持网址登录、自动扫描和智能抢夺。

## 🌟 功能特点

- � **网址登录** - 输入直播间网址，自动解析房间信息
- 🔍 **智能扫描** - 实时监控福袋状态，自动发现新福袋
- ⚡ **快速抢夺** - 毫秒级响应，提高抢夺成功率
- 🖥️ **图形界面** - 友好的GUI操作界面，支持实时状态显示
- 🍪 **自动登录** - 支持从浏览器获取Cookie，保持登录状态
- � **实时统计** - 显示福袋数量、抢夺状态等信息
- �📝 **详细日志** - 完整的运行日志和错误记录
- ⚙️ **灵活配置** - 可调整抢夺间隔、最大尝试次数等参数
- 🔄 **多线程** - 高效的并发处理机制
- � **智能去重** - 避免重复抢夺同一个福袋

## 文件说明

- `lucky_bag_grabber.py` - 核心抢夺逻辑（命令行版本）
- `lucky_bag_gui.py` - 图形界面版本
- `config.json` - 配置文件
- `README.md` - 使用说明

## 安装依赖

```bash
pip install requests tkinter
```

## 🚀 快速开始

### 方法一：一键启动（推荐）

双击 `start.bat` 文件，选择运行模式：
- 自动检查Python环境和依赖
- 提供使用提示和帮助

### 方法二：图形界面版本（推荐）

```bash
python lucky_bag_gui.py
```

**详细步骤：**
1. **输入网址** - 在"直播间网址"框中输入完整的直播间URL
2. **解析房间** - 点击"解析房间"按钮，自动获取房间ID和信息
3. **浏览器登录** - 点击"打开浏览器登录"，在浏览器中登录账号
4. **获取Cookie** - 返回软件，点击"从浏览器获取Cookie"，按提示操作
5. **开始抢夺** - 调整参数后，点击"开始抢福袋"

### 方法三：命令行版本

```bash
python lucky_bag_grabber.py
```

按提示输入：
- 房间ID或直播间网址
- Cookie（建议设置）
- 抢夺间隔

## 🍪 获取Cookie详细方法

### 自动方式（推荐）
1. 在软件中点击"打开浏览器登录"
2. 在打开的浏览器中登录账号
3. 返回软件点击"从浏览器获取Cookie"
4. 按照弹出的详细说明操作

### 手动方式
1. 打开浏览器，登录目标网站
2. 按 `F12` 打开开发者工具
3. 切换到 `Network`(网络) 标签
4. 刷新页面或进行任意操作
5. 找到任意请求，点击查看详情
6. 在 `Request Headers` 中找到 `Cookie`
7. 复制完整的Cookie字符串到软件中

### 支持的直播平台
- 🎮 **斗鱼直播** - douyu.com
- 🐯 **虎牙直播** - huya.com
- 📺 **哔哩哔哩** - live.bilibili.com
- 🎪 **其他平台** - 可手动设置房间ID

## 配置说明

### 抢夺间隔
- 建议设置为0.1-0.5秒
- 过快可能被限制，过慢可能错过福袋

### 最大尝试次数
- 防止无限循环
- 建议设置为100-1000

## 注意事项

⚠️ **重要提醒**

1. **仅供学习研究使用**，请勿用于商业用途
2. **遵守网站服务条款**，避免对服务器造成过大压力
3. **合理设置抢夺间隔**，避免被反爬虫机制检测
4. **使用风险自负**，作者不承担任何责任

## 常见问题

### Q: 为什么抢不到福袋？
A: 可能的原因：
- 未设置Cookie，无登录状态
- 抢夺间隔过长
- 网络延迟过高
- 福袋数量有限，竞争激烈

### Q: 如何提高成功率？
A: 建议：
- 设置有效的Cookie
- 减小抢夺间隔（但不要过小）
- 确保网络连接稳定
- 选择人数较少的直播间

### Q: 脚本停止运行怎么办？
A: 检查：
- 网络连接是否正常
- Cookie是否过期
- 房间ID是否正确
- 查看日志文件了解具体错误

## 技术原理

1. **监控机制**：定时请求API检查福袋状态
2. **抢夺逻辑**：发现福袋后立即发送参与请求
3. **并发处理**：使用多线程提高响应速度
4. **错误处理**：完善的异常捕获和重试机制

## 免责声明

本脚本仅供技术学习和研究使用。使用者应当：

1. 遵守相关网站的服务条款和使用协议
2. 不得将此脚本用于任何违法违规的活动
3. 承担使用此脚本可能带来的所有风险和后果
4. 尊重他人权益，不得恶意刷取或影响正常用户体验

作者不对使用此脚本造成的任何直接或间接损失承担责任。

## 更新日志

### v1.0
- 初始版本
- 支持基本的福袋抢夺功能
- 提供GUI和命令行两种界面
- 完善的日志记录和错误处理

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交Issue
- 发送邮件

---

**请合理使用，遵守相关规定！**
