#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抢福袋自动化脚本 - 演示程序
Lucky Bag Auto Grabber - Demo
"""

import time
import random
from lucky_bag_grabber import LuckyBagGrabber

def demo_url_parsing():
    """演示URL解析功能"""
    print("=" * 50)
    print("URL解析功能演示")
    print("=" * 50)
    
    test_urls = [
        "https://www.douyu.com/123456",
        "https://www.huya.com/testroom",
        "https://live.bilibili.com/789012",
        "douyu.com/999888",  # 不带协议
    ]
    
    import re
    
    for url in test_urls:
        print(f"\n测试URL: {url}")
        
        # 斗鱼直播间URL解析
        if 'douyu.com' in url:
            match = re.search(r'douyu\.com/(\d+)', url)
            if match:
                room_id = match.group(1)
                print(f"  ✅ 解析成功 - 斗鱼房间ID: {room_id}")
            else:
                print(f"  ❌ 解析失败")
        
        # 虎牙直播间URL解析
        elif 'huya.com' in url:
            match = re.search(r'huya\.com/(\w+)', url)
            if match:
                room_id = match.group(1)
                print(f"  ✅ 解析成功 - 虎牙房间ID: {room_id}")
            else:
                print(f"  ❌ 解析失败")
        
        # Bilibili直播间URL解析
        elif 'live.bilibili.com' in url:
            match = re.search(r'live\.bilibili\.com/(\d+)', url)
            if match:
                room_id = match.group(1)
                print(f"  ✅ 解析成功 - B站房间ID: {room_id}")
            else:
                print(f"  ❌ 解析失败")

def demo_grabber_basic():
    """演示基本抢夺功能"""
    print("\n" + "=" * 50)
    print("基本抢夺功能演示")
    print("=" * 50)
    
    grabber = LuckyBagGrabber()
    
    # 设置测试房间ID
    test_room_id = "123456"
    grabber.set_room_id(test_room_id)
    print(f"✅ 房间ID设置为: {test_room_id}")
    
    # 设置抢夺间隔
    grabber.set_grab_interval(0.5)
    print(f"✅ 抢夺间隔设置为: 0.5秒")
    
    # 模拟Cookie设置
    test_cookie = "test_session=abc123; user_id=456789"
    grabber.set_cookies(test_cookie)
    print(f"✅ Cookie设置完成")
    
    print(f"\n📊 当前配置:")
    print(f"  - 房间ID: {grabber.room_id}")
    print(f"  - 抢夺间隔: {grabber.grab_interval}秒")
    print(f"  - 最大尝试次数: {grabber.max_attempts}")
    print(f"  - Cookie数量: {len(grabber.cookies)}个")

def demo_mock_lucky_bags():
    """演示模拟福袋检测"""
    print("\n" + "=" * 50)
    print("模拟福袋检测演示")
    print("=" * 50)
    
    # 模拟福袋数据
    mock_bags = [
        {
            "lottery_id": "bag_001",
            "lottery_title": "新年福袋",
            "lottery_type": "金币福袋",
            "status": 1,
            "end_time": int(time.time()) + 300
        },
        {
            "lottery_id": "bag_002", 
            "lottery_title": "幸运抽奖",
            "lottery_type": "礼物福袋",
            "status": 1,
            "end_time": int(time.time()) + 180
        }
    ]
    
    print(f"🎁 发现 {len(mock_bags)} 个福袋:")
    
    for i, bag in enumerate(mock_bags, 1):
        print(f"\n福袋 {i}:")
        print(f"  ID: {bag['lottery_id']}")
        print(f"  名称: {bag['lottery_title']}")
        print(f"  类型: {bag['lottery_type']}")
        print(f"  状态: {'进行中' if bag['status'] == 1 else '已结束'}")
        
        # 模拟抢夺过程
        print(f"  🎯 正在抢夺...")
        time.sleep(0.5)  # 模拟网络延迟
        
        # 随机成功/失败
        success = random.choice([True, False, True])  # 66%成功率
        if success:
            print(f"  ✅ 抢夺成功!")
        else:
            print(f"  ❌ 抢夺失败 (可能已被抢完)")

def demo_gui_features():
    """演示GUI功能特点"""
    print("\n" + "=" * 50)
    print("GUI功能特点演示")
    print("=" * 50)
    
    features = [
        "🌐 网址输入 - 支持直接输入直播间URL",
        "🔍 自动解析 - 智能识别房间ID和平台",
        "🖱️ 一键登录 - 打开浏览器进行登录",
        "🍪 Cookie获取 - 自动获取登录状态",
        "📊 实时监控 - 显示福袋数量和状态",
        "📝 详细日志 - 完整的操作记录",
        "⚙️ 参数配置 - 灵活的抢夺设置",
        "🎯 智能抢夺 - 避免重复和提高成功率"
    ]
    
    for feature in features:
        print(f"  {feature}")
        time.sleep(0.3)

def main():
    """主演示函数"""
    print("🎉 抢福袋自动化脚本 v2.0 - 功能演示")
    print("=" * 60)
    
    try:
        # URL解析演示
        demo_url_parsing()
        
        # 基本功能演示
        demo_grabber_basic()
        
        # 模拟福袋检测
        demo_mock_lucky_bags()
        
        # GUI功能介绍
        demo_gui_features()
        
        print("\n" + "=" * 60)
        print("🎊 演示完成!")
        print("\n💡 使用提示:")
        print("  1. 运行 'python lucky_bag_gui.py' 启动图形界面")
        print("  2. 或双击 'start.bat' 选择运行模式")
        print("  3. 按照界面提示进行操作")
        print("\n⚠️  注意: 请遵守相关网站服务条款，合理使用!")
        
    except KeyboardInterrupt:
        print("\n\n👋 演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")

if __name__ == "__main__":
    main()
