#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抢福袋自动化脚本
Lucky Bag Auto Grabber
"""

import time
import random
import requests
import json
import threading
from datetime import datetime
from typing import Dict, List, Optional
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('lucky_bag.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class LuckyBagGrabber:
    """抢福袋自动化类"""
    
    def __init__(self):
        self.session = requests.Session()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Content-Type': 'application/json',
            'Referer': 'https://www.douyu.com/',
        }
        self.session.headers.update(self.headers)
        
        # 配置参数
        self.room_id = None
        self.cookies = {}
        self.is_running = False
        self.grab_interval = 0.1  # 抢夺间隔（秒）
        self.max_attempts = 100   # 最大尝试次数
        
    def set_cookies(self, cookie_string: str):
        """设置Cookie"""
        try:
            if cookie_string:
                cookie_pairs = cookie_string.split(';')
                for pair in cookie_pairs:
                    if '=' in pair:
                        key, value = pair.strip().split('=', 1)
                        self.cookies[key] = value
                self.session.cookies.update(self.cookies)
                logger.info("Cookie设置成功")
                return True
        except Exception as e:
            logger.error(f"Cookie设置失败: {e}")
        return False
    
    def set_room_id(self, room_id: str):
        """设置房间ID"""
        self.room_id = room_id
        logger.info(f"房间ID设置为: {room_id}")
    
    def get_room_info(self) -> Optional[Dict]:
        """获取房间信息"""
        if not self.room_id:
            logger.error("房间ID未设置")
            return None
            
        try:
            url = f"https://www.douyu.com/betard/{self.room_id}"
            response = self.session.get(url, timeout=10)
            if response.status_code == 200:
                data = response.json()
                logger.info(f"房间信息获取成功: {data.get('room', {}).get('room_name', 'Unknown')}")
                return data
        except Exception as e:
            logger.error(f"获取房间信息失败: {e}")
        return None
    
    def check_lucky_bag(self) -> List[Dict]:
        """检查福袋信息"""
        if not self.room_id:
            return []
            
        try:
            # 这里需要根据实际的API接口来调整
            url = f"https://www.douyu.com/lapi/lottery/api/getLotteryInfo"
            params = {
                'rid': self.room_id,
                'time': int(time.time() * 1000)
            }
            
            response = self.session.get(url, params=params, timeout=5)
            if response.status_code == 200:
                data = response.json()
                if data.get('error') == 0:
                    lottery_list = data.get('data', {}).get('lottery_list', [])
                    active_bags = [bag for bag in lottery_list if bag.get('status') == 1]
                    return active_bags
        except Exception as e:
            logger.debug(f"检查福袋失败: {e}")
        return []
    
    def grab_lucky_bag(self, bag_id: str) -> bool:
        """抢夺福袋"""
        try:
            url = "https://www.douyu.com/lapi/lottery/api/joinLottery"
            data = {
                'rid': self.room_id,
                'lottery_id': bag_id,
                'time': int(time.time() * 1000)
            }
            
            response = self.session.post(url, json=data, timeout=5)
            if response.status_code == 200:
                result = response.json()
                if result.get('error') == 0:
                    logger.info(f"福袋 {bag_id} 抢夺成功!")
                    return True
                else:
                    logger.warning(f"福袋 {bag_id} 抢夺失败: {result.get('msg', 'Unknown error')}")
        except Exception as e:
            logger.error(f"抢夺福袋 {bag_id} 时发生错误: {e}")
        return False
    
    def auto_grab_loop(self):
        """自动抢夺循环"""
        logger.info("开始自动抢福袋...")
        attempt_count = 0
        
        while self.is_running and attempt_count < self.max_attempts:
            try:
                # 检查福袋
                lucky_bags = self.check_lucky_bag()
                
                if lucky_bags:
                    logger.info(f"发现 {len(lucky_bags)} 个福袋")
                    for bag in lucky_bags:
                        if not self.is_running:
                            break
                        bag_id = bag.get('lottery_id')
                        bag_name = bag.get('lottery_title', 'Unknown')
                        logger.info(f"尝试抢夺福袋: {bag_name} (ID: {bag_id})")
                        self.grab_lucky_bag(bag_id)
                        time.sleep(0.05)  # 短暂延迟避免请求过快
                
                attempt_count += 1
                time.sleep(self.grab_interval)
                
            except KeyboardInterrupt:
                logger.info("用户中断操作")
                break
            except Exception as e:
                logger.error(f"自动抢夺循环出错: {e}")
                time.sleep(1)
        
        logger.info("自动抢福袋结束")
    
    def start_auto_grab(self):
        """开始自动抢夺"""
        if not self.room_id:
            logger.error("请先设置房间ID")
            return False
            
        if not self.cookies:
            logger.warning("未设置Cookie，可能无法正常抢夺")
        
        self.is_running = True
        self.grab_thread = threading.Thread(target=self.auto_grab_loop)
        self.grab_thread.daemon = True
        self.grab_thread.start()
        return True
    
    def stop_auto_grab(self):
        """停止自动抢夺"""
        self.is_running = False
        logger.info("正在停止自动抢福袋...")
    
    def set_grab_interval(self, interval: float):
        """设置抢夺间隔"""
        self.grab_interval = max(0.05, interval)  # 最小间隔0.05秒
        logger.info(f"抢夺间隔设置为: {self.grab_interval}秒")

def main():
    """主函数 - 命令行界面"""
    grabber = LuckyBagGrabber()
    
    print("=" * 50)
    print("抢福袋自动化脚本 v1.0")
    print("=" * 50)
    
    # 设置房间ID
    room_id = input("请输入房间ID: ").strip()
    if not room_id:
        print("房间ID不能为空")
        return
    grabber.set_room_id(room_id)
    
    # 设置Cookie（可选）
    cookie_input = input("请输入Cookie (可选，直接回车跳过): ").strip()
    if cookie_input:
        grabber.set_cookies(cookie_input)
    
    # 设置抢夺间隔
    try:
        interval = float(input("请输入抢夺间隔(秒，默认0.1): ") or "0.1")
        grabber.set_grab_interval(interval)
    except ValueError:
        print("使用默认间隔0.1秒")
    
    print("\n开始监控福袋...")
    print("按 Ctrl+C 停止")
    
    try:
        grabber.start_auto_grab()
        
        # 保持主线程运行
        while grabber.is_running:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n正在停止...")
        grabber.stop_auto_grab()
        time.sleep(1)
        print("已停止")

if __name__ == "__main__":
    main()
